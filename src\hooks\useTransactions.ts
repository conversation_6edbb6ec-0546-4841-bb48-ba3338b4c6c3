import { useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { 
  setLoading, 
  setError, 
  setTransactions, 
  addTransaction as addTransactionAction,
  updateTransaction as updateTransactionAction,
  removeTransaction as removeTransactionAction
} from '../store/slices/transactionsSlice';
import { 
  getTransactions, 
  storeTransactions, 
  addTransaction as storeAddTransaction,
  updateTransaction as storeUpdateTransaction,
  deleteTransaction as storeDeleteTransaction
} from '../services/storage';
import { Transaction } from '../types';
import { generateId, validateTransaction } from '../utils';

export const useTransactions = () => {
  const dispatch = useDispatch();
  const transactions = useSelector((state: RootState) => state.transactions);

  /**
   * Load transactions from storage
   */
  const loadTransactions = useCallback(async () => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      const storedTransactions = await getTransactions();
      dispatch(setTransactions(storedTransactions));
    } catch (error) {
      console.error('Error loading transactions:', error);
      dispatch(setError(error instanceof Error ? error.message : 'Failed to load transactions'));
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Add a new transaction
   */
  const addTransaction = useCallback(async (transactionData: Omit<Transaction, 'id'>) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      // Validate transaction data
      const validationErrors = validateTransaction(transactionData);
      if (validationErrors.length > 0) {
        throw new Error(validationErrors.join(', '));
      }

      // Create transaction with ID
      const transaction: Transaction = {
        ...transactionData,
        id: generateId(),
      };

      // Store in local storage
      await storeAddTransaction(transaction);

      // Update Redux state
      dispatch(addTransactionAction(transaction));

      return transaction;
    } catch (error) {
      console.error('Error adding transaction:', error);
      dispatch(setError(error instanceof Error ? error.message : 'Failed to add transaction'));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Update an existing transaction
   */
  const updateTransaction = useCallback(async (
    transactionId: string, 
    updates: Partial<Omit<Transaction, 'id'>>
  ) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      // Validate updated transaction data
      const currentTransaction = transactions.transactions.find(tx => tx.id === transactionId);
      if (!currentTransaction) {
        throw new Error('Transaction not found');
      }

      const updatedTransaction = { ...currentTransaction, ...updates };
      const validationErrors = validateTransaction(updatedTransaction);
      if (validationErrors.length > 0) {
        throw new Error(validationErrors.join(', '));
      }

      // Update in local storage
      await storeUpdateTransaction(transactionId, updates);

      // Update Redux state
      dispatch(updateTransactionAction({ id: transactionId, updates }));

      return updatedTransaction;
    } catch (error) {
      console.error('Error updating transaction:', error);
      dispatch(setError(error instanceof Error ? error.message : 'Failed to update transaction'));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch, transactions.transactions]);

  /**
   * Delete a transaction
   */
  const deleteTransaction = useCallback(async (transactionId: string) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      // Delete from local storage
      await storeDeleteTransaction(transactionId);

      // Update Redux state
      dispatch(removeTransactionAction(transactionId));
    } catch (error) {
      console.error('Error deleting transaction:', error);
      dispatch(setError(error instanceof Error ? error.message : 'Failed to delete transaction'));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Import multiple transactions
   */
  const importTransactions = useCallback(async (transactionsToImport: Omit<Transaction, 'id'>[]) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      // Validate all transactions
      const validationErrors: string[] = [];
      transactionsToImport.forEach((tx, index) => {
        const errors = validateTransaction(tx);
        if (errors.length > 0) {
          validationErrors.push(`Transaction ${index + 1}: ${errors.join(', ')}`);
        }
      });

      if (validationErrors.length > 0) {
        throw new Error(validationErrors.join('\n'));
      }

      // Add IDs to transactions
      const transactionsWithIds: Transaction[] = transactionsToImport.map(tx => ({
        ...tx,
        id: generateId(),
      }));

      // Get current transactions and merge
      const currentTransactions = await getTransactions();
      const allTransactions = [...currentTransactions, ...transactionsWithIds];

      // Store all transactions
      await storeTransactions(allTransactions);

      // Update Redux state
      dispatch(setTransactions(allTransactions));

      return transactionsWithIds;
    } catch (error) {
      console.error('Error importing transactions:', error);
      dispatch(setError(error instanceof Error ? error.message : 'Failed to import transactions'));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Get transactions filtered by criteria
   */
  const getFilteredTransactions = useCallback((filters: {
    type?: Transaction['type'];
    cryptoId?: string;
    startDate?: string;
    endDate?: string;
  }) => {
    let filtered = transactions.transactions;

    if (filters.type) {
      filtered = filtered.filter(tx => tx.type === filters.type);
    }

    if (filters.cryptoId) {
      filtered = filtered.filter(tx => tx.cryptocurrency.id === filters.cryptoId);
    }

    if (filters.startDate) {
      filtered = filtered.filter(tx => new Date(tx.date) >= new Date(filters.startDate!));
    }

    if (filters.endDate) {
      filtered = filtered.filter(tx => new Date(tx.date) <= new Date(filters.endDate!));
    }

    return filtered;
  }, [transactions.transactions]);

  /**
   * Load transactions on mount
   */
  useEffect(() => {
    loadTransactions();
  }, [loadTransactions]);

  return {
    // State
    transactions: transactions.transactions,
    isLoading: transactions.isLoading,
    error: transactions.error,
    lastUpdated: transactions.lastUpdated,
    
    // Actions
    loadTransactions,
    addTransaction,
    updateTransaction,
    deleteTransaction,
    importTransactions,
    getFilteredTransactions,
  };
};
