import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import { COLORS } from '../../constants';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Loading } from '../../components/ui/Loading';
import { useLunoAccount } from '../../hooks/useLunoAccount';

export default function SettingsScreen() {
  const [apiKeyId, setApiKeyId] = useState('');
  const [apiKeySecret, setApiKeySecret] = useState('');
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  const {
    isConnected,
    isLoading,
    error,
    connectToLuno,
    disconnectFromLuno,
    checkConnection,
    syncWithPortfolio,
  } = useLunoAccount();

  const handleSaveCredentials = async () => {
    if (!apiKeyId.trim() || !apiKeySecret.trim()) {
      Alert.alert('Error', 'Please enter both API Key ID and API Key Secret');
      return;
    }

    setIsTestingConnection(true);
    try {
      const success = await connectToLuno(apiKeyId.trim(), apiKeySecret.trim());
      if (success) {
        Alert.alert('Success', 'Luno API credentials saved successfully!');
        setApiKeyId('');
        setApiKeySecret('');
        // Sync with portfolio after successful connection
        await syncWithPortfolio();
      }
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to save credentials');
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleDisconnect = async () => {
    Alert.alert(
      'Disconnect Luno Account',
      'Are you sure you want to disconnect your Luno account? This will remove your API credentials.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Disconnect',
          style: 'destructive',
          onPress: async () => {
            const success = await disconnectFromLuno();
            if (success) {
              Alert.alert('Success', 'Luno account disconnected');
            } else {
              Alert.alert('Error', 'Failed to disconnect account');
            }
          },
        },
      ]
    );
  };

  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    try {
      const connected = await checkConnection();
      if (connected) {
        Alert.alert('Success', 'Connection to Luno API successful!');
      } else {
        Alert.alert('Error', 'Failed to connect to Luno API');
      }
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'Connection test failed');
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSyncPortfolio = async () => {
    try {
      await syncWithPortfolio();
      Alert.alert('Success', 'Portfolio synced with Luno account!');
    } catch (error) {
      Alert.alert('Error', 'Failed to sync portfolio');
    }
  };

  if (isLoading) {
    return <Loading text="Checking connection..." />;
  }

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Text style={styles.title}>Luno Account Connection</Text>
        
        {isConnected ? (
          <View style={styles.connectedSection}>
            <View style={styles.statusContainer}>
              <View style={styles.statusIndicator} />
              <Text style={styles.statusText}>Connected to Luno</Text>
            </View>
            
            <Text style={styles.description}>
              Your Luno account is connected. The app will sync your real portfolio data.
            </Text>
            
            <View style={styles.buttonContainer}>
              <Button
                title="Sync Portfolio"
                onPress={handleSyncPortfolio}
                loading={isLoading}
                variant="secondary"
                style={styles.button}
              />
              <Button
                title="Test Connection"
                onPress={handleTestConnection}
                loading={isTestingConnection}
                variant="outline"
                style={styles.button}
              />
            </View>

            <View style={styles.buttonContainer}>
              <Button
                title="Disconnect"
                onPress={handleDisconnect}
                variant="danger"
                fullWidth
                style={styles.disconnectButton}
              />
            </View>
          </View>
        ) : (
          <View style={styles.setupSection}>
            <Text style={styles.description}>
              Connect your Luno account to automatically sync your real portfolio data.
            </Text>
            
            <Text style={styles.instructionsTitle}>Setup Instructions:</Text>
            <Text style={styles.instructions}>
              1. Log in to your Luno account{'\n'}
              2. Go to Settings → API Keys{'\n'}
              3. Create a new API key with "Read" permissions{'\n'}
              4. Copy the API Key ID and Secret below
            </Text>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>API Key ID</Text>
              <TextInput
                style={styles.input}
                value={apiKeyId}
                onChangeText={setApiKeyId}
                placeholder="Enter your Luno API Key ID"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>API Key Secret</Text>
              <TextInput
                style={styles.input}
                value={apiKeySecret}
                onChangeText={setApiKeySecret}
                placeholder="Enter your Luno API Key Secret"
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
            
            <Button
              title="Connect to Luno"
              onPress={handleSaveCredentials}
              loading={isTestingConnection}
              fullWidth
              style={styles.connectButton}
            />
          </View>
        )}
      </Card>

      <Card style={styles.card}>
        <Text style={styles.title}>Security Notice</Text>
        <Text style={styles.securityText}>
          • Your API credentials are stored securely on your device{'\n'}
          • We recommend using API keys with read-only permissions{'\n'}
          • Never share your API credentials with anyone{'\n'}
          • You can disconnect at any time
        </Text>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.surface,
  },
  card: {
    margin: 16,
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: COLORS.textSecondary,
    lineHeight: 24,
    marginBottom: 16,
  },
  connectedSection: {
    alignItems: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: COLORS.success,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.success,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 16,
  },
  button: {
    flex: 1,
    marginHorizontal: 8,
  },
  setupSection: {
    // No specific styles needed
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 8,
  },
  instructions: {
    fontSize: 14,
    color: COLORS.textSecondary,
    lineHeight: 20,
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.text,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: COLORS.text,
    backgroundColor: COLORS.background,
  },
  connectButton: {
    marginTop: 8,
  },
  disconnectButton: {
    marginTop: 8,
  },
  securityText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
});
