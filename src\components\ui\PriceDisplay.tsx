import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants';
import { formatCurrency, formatPercentage, getPercentageColor } from '../../utils';

interface PriceDisplayProps {
  price: number;
  change?: number;
  changePercentage?: number;
  currency?: 'USD' | 'EUR' | 'GBP' | 'ZAR';
  size?: 'small' | 'medium' | 'large';
  showChange?: boolean;
  showIcon?: boolean;
  style?: ViewStyle;
  priceStyle?: TextStyle;
  changeStyle?: TextStyle;
}

export const PriceDisplay: React.FC<PriceDisplayProps> = ({
  price,
  change,
  changePercentage,
  currency = 'USD',
  size = 'medium',
  showChange = true,
  showIcon = true,
  style,
  priceStyle,
  changeStyle,
}) => {
  const hasChange = change !== undefined || changePercentage !== undefined;
  const isPositive = (change || 0) >= 0 && (changePercentage || 0) >= 0;
  const changeColor = getPercentageColor(changePercentage || change || 0);

  const getPriceTextStyle = (): TextStyle => {
    switch (size) {
      case 'small':
        return styles.smallPrice;
      case 'large':
        return styles.largePrice;
      default:
        return styles.mediumPrice;
    }
  };

  const getChangeTextStyle = (): TextStyle => {
    switch (size) {
      case 'small':
        return styles.smallChange;
      case 'large':
        return styles.largeChange;
      default:
        return styles.mediumChange;
    }
  };

  const getIconSize = (): number => {
    switch (size) {
      case 'small':
        return 12;
      case 'large':
        return 20;
      default:
        return 16;
    }
  };

  return (
    <View style={[styles.container, style]}>
      <Text style={[styles.price, getPriceTextStyle(), priceStyle]}>
        {formatCurrency(price, currency)}
      </Text>
      
      {showChange && hasChange && (
        <View style={styles.changeContainer}>
          {showIcon && (
            <Ionicons
              name={isPositive ? 'trending-up' : 'trending-down'}
              size={getIconSize()}
              color={changeColor}
              style={styles.changeIcon}
            />
          )}
          <Text style={[
            styles.change,
            getChangeTextStyle(),
            { color: changeColor },
            changeStyle
          ]}>
            {changePercentage !== undefined 
              ? formatPercentage(changePercentage)
              : change !== undefined 
                ? formatCurrency(Math.abs(change), currency)
                : ''
            }
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'flex-start',
  },
  price: {
    fontWeight: 'bold',
    color: COLORS.text,
  },
  smallPrice: {
    fontSize: 14,
  },
  mediumPrice: {
    fontSize: 18,
  },
  largePrice: {
    fontSize: 24,
  },
  changeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  changeIcon: {
    marginRight: 2,
  },
  change: {
    fontWeight: '600',
  },
  smallChange: {
    fontSize: 12,
  },
  mediumChange: {
    fontSize: 14,
  },
  largeChange: {
    fontSize: 16,
  },
});
