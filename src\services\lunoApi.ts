import axios, { AxiosResponse } from 'axios';
import { LUNO_CONFIG, API_CONFIG } from '../constants';
import { getData, storeData, STORAGE_KEYS } from './storage';

// Luno API credentials interface
interface LunoCredentials {
  apiKeyId: string;
  apiKeySecret: string;
}

// Luno API response types
interface LunoBalance {
  account_id: string;
  asset: string;
  balance: string;
  reserved: string;
  unconfirmed: string;
}

interface LunoTransaction {
  row_index: number;
  timestamp: number;
  balance: string;
  available: string;
  available_delta: string;
  balance_delta: string;
  currency: string;
  description: string;
  details: {
    [key: string]: any;
  };
}

interface LunoTicker {
  pair: string;
  timestamp: number;
  bid: string;
  ask: string;
  last_trade: string;
  rolling_24_hour_volume: string;
  status: string;
}

/**
 * Luno API Service for connecting to your actual Luno account
 */
class LunoApiService {
  private credentials: LunoCredentials | null = null;
  private axiosInstance = axios.create({
    baseURL: API_CONFIG.LUNO.BASE_URL,
    timeout: 10000,
  });

  constructor() {
    this.setupInterceptors();
    this.loadCredentials();
  }

  /**
   * Setup axios interceptors for authentication and error handling
   */
  private setupInterceptors() {
    // Request interceptor for authentication
    this.axiosInstance.interceptors.request.use(
      (config) => {
        if (this.credentials) {
          const auth = Buffer.from(
            `${this.credentials.apiKeyId}:${this.credentials.apiKeySecret}`
          ).toString('base64');
          config.headers.Authorization = `Basic ${auth}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('Luno API Error:', error.response?.data || error.message);
        
        if (error.response?.status === 401) {
          throw new Error('Invalid Luno API credentials. Please check your API key and secret.');
        }
        
        if (error.response?.status === 403) {
          throw new Error('Insufficient permissions. Please check your API key permissions.');
        }
        
        throw new Error(error.response?.data?.error || error.message || 'Luno API request failed');
      }
    );
  }

  /**
   * Load stored credentials
   */
  private async loadCredentials() {
    try {
      const stored = await getData<LunoCredentials>(STORAGE_KEYS.LUNO_API_CREDENTIALS);
      if (stored) {
        this.credentials = stored;
      }
    } catch (error) {
      console.error('Error loading Luno credentials:', error);
    }
  }

  /**
   * Set and store API credentials
   */
  async setCredentials(apiKeyId: string, apiKeySecret: string): Promise<void> {
    const credentials: LunoCredentials = { apiKeyId, apiKeySecret };
    
    try {
      // Test the credentials by making a simple API call
      this.credentials = credentials;
      await this.getBalance(); // This will throw if credentials are invalid
      
      // Store credentials if test succeeds
      await storeData(STORAGE_KEYS.LUNO_API_CREDENTIALS, credentials);
    } catch (error) {
      this.credentials = null;
      throw new Error('Invalid API credentials. Please check your Luno API key and secret.');
    }
  }

  /**
   * Check if credentials are set
   */
  hasCredentials(): boolean {
    return this.credentials !== null;
  }

  /**
   * Clear stored credentials
   */
  async clearCredentials(): Promise<void> {
    this.credentials = null;
    await storeData(STORAGE_KEYS.LUNO_API_CREDENTIALS, null);
  }

  /**
   * Get account balance
   */
  async getBalance(): Promise<LunoBalance[]> {
    if (!this.credentials) {
      throw new Error('Luno API credentials not set');
    }

    try {
      const response: AxiosResponse<{ balance: LunoBalance[] }> = await this.axiosInstance.get(
        API_CONFIG.LUNO.ENDPOINTS.BALANCE
      );
      return response.data.balance;
    } catch (error) {
      console.error('Error fetching Luno balance:', error);
      throw error;
    }
  }

  /**
   * Get account transactions
   */
  async getTransactions(
    asset?: string,
    minRow?: number,
    maxRow?: number
  ): Promise<LunoTransaction[]> {
    if (!this.credentials) {
      throw new Error('Luno API credentials not set');
    }

    try {
      const params: any = {};
      if (asset) params.asset = asset;
      if (minRow) params.min_row = minRow;
      if (maxRow) params.max_row = maxRow;

      const response: AxiosResponse<{ transactions: LunoTransaction[] }> = 
        await this.axiosInstance.get(API_CONFIG.LUNO.ENDPOINTS.ACCOUNT_TRANSACTIONS, { params });
      
      return response.data.transactions;
    } catch (error) {
      console.error('Error fetching Luno transactions:', error);
      throw error;
    }
  }

  /**
   * Get market tickers
   */
  async getTickers(): Promise<LunoTicker[]> {
    try {
      const response: AxiosResponse<{ tickers: LunoTicker[] }> = 
        await this.axiosInstance.get(API_CONFIG.LUNO.ENDPOINTS.TICKERS);
      
      return response.data.tickers;
    } catch (error) {
      console.error('Error fetching Luno tickers:', error);
      throw error;
    }
  }

  /**
   * Get ticker for specific pair
   */
  async getTicker(pair: string): Promise<LunoTicker> {
    try {
      const response: AxiosResponse<LunoTicker> = 
        await this.axiosInstance.get(`${API_CONFIG.LUNO.ENDPOINTS.TICKERS}?pair=${pair}`);
      
      return response.data;
    } catch (error) {
      console.error('Error fetching Luno ticker:', error);
      throw error;
    }
  }

  /**
   * Convert Luno balance to our portfolio format
   */
  convertBalanceToHoldings(balances: LunoBalance[]): any[] {
    return balances
      .filter(balance => parseFloat(balance.balance) > 0)
      .map(balance => {
        const cryptoId = LUNO_CONFIG.CURRENCY_MAPPING[balance.asset as keyof typeof LUNO_CONFIG.CURRENCY_MAPPING];
        
        return {
          id: `luno_${balance.account_id}`,
          cryptocurrency: {
            id: cryptoId || balance.asset.toLowerCase(),
            symbol: balance.asset,
            name: balance.asset,
          },
          amount: parseFloat(balance.balance),
          availableAmount: parseFloat(balance.balance) - parseFloat(balance.reserved),
          reservedAmount: parseFloat(balance.reserved),
          unconfirmedAmount: parseFloat(balance.unconfirmed),
          source: 'luno',
          lastUpdated: new Date().toISOString(),
        };
      });
  }

  /**
   * Convert Luno transactions to our transaction format
   */
  convertTransactionsToPortfolioFormat(transactions: LunoTransaction[]): any[] {
    return transactions.map(tx => ({
      id: `luno_${tx.row_index}`,
      type: this.determineTransactionType(tx),
      cryptocurrency: {
        id: LUNO_CONFIG.CURRENCY_MAPPING[tx.currency as keyof typeof LUNO_CONFIG.CURRENCY_MAPPING] || tx.currency.toLowerCase(),
        symbol: tx.currency,
        name: tx.currency,
      },
      amount: Math.abs(parseFloat(tx.balance_delta)),
      date: new Date(tx.timestamp * 1000).toISOString(),
      description: tx.description,
      details: tx.details,
      source: 'luno',
    }));
  }

  /**
   * Determine transaction type from Luno transaction
   */
  private determineTransactionType(tx: LunoTransaction): string {
    const delta = parseFloat(tx.balance_delta);
    const description = tx.description.toLowerCase();
    
    if (description.includes('buy') || description.includes('purchase')) {
      return 'buy';
    } else if (description.includes('sell') || description.includes('sale')) {
      return 'sell';
    } else if (delta > 0) {
      return 'transfer_in';
    } else {
      return 'transfer_out';
    }
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.getBalance();
      return true;
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const lunoApiService = new LunoApiService();
export default lunoApiService;
