import { useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { 
  setLoading, 
  setError, 
  setHoldings, 
  setSummary, 
  updateHoldingPrices 
} from '../store/slices/portfolioSlice';
import { 
  getPortfolioData, 
  refreshPortfolioData, 
  updatePortfolioPrices 
} from '../services/portfolioService';

export const usePortfolio = () => {
  const dispatch = useDispatch();
  const portfolio = useSelector((state: RootState) => state.portfolio);

  /**
   * Load portfolio data from storage
   */
  const loadPortfolio = useCallback(async (forceRefresh: boolean = false) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      const { holdings, summary } = await getPortfolioData(forceRefresh);
      
      dispatch(setHoldings(holdings));
      dispatch(setSummary(summary));
    } catch (error) {
      console.error('Error loading portfolio:', error);
      dispatch(setError(error instanceof Error ? error.message : 'Failed to load portfolio'));
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Refresh portfolio with latest prices
   */
  const refreshPrices = useCallback(async () => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      const updatedHoldings = await updatePortfolioPrices();
      dispatch(setHoldings(updatedHoldings));
      
      // Recalculate summary
      const { summary } = await getPortfolioData();
      dispatch(setSummary(summary));
    } catch (error) {
      console.error('Error refreshing prices:', error);
      dispatch(setError(error instanceof Error ? error.message : 'Failed to refresh prices'));
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Refresh entire portfolio data
   */
  const refreshPortfolio = useCallback(async () => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      const { holdings, summary } = await refreshPortfolioData();
      
      dispatch(setHoldings(holdings));
      dispatch(setSummary(summary));
    } catch (error) {
      console.error('Error refreshing portfolio:', error);
      dispatch(setError(error instanceof Error ? error.message : 'Failed to refresh portfolio'));
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Load portfolio on mount
   */
  useEffect(() => {
    loadPortfolio();
  }, [loadPortfolio]);

  return {
    // State
    holdings: portfolio.holdings,
    summary: portfolio.summary,
    isLoading: portfolio.isLoading,
    error: portfolio.error,
    lastUpdated: portfolio.lastUpdated,
    
    // Actions
    loadPortfolio,
    refreshPrices,
    refreshPortfolio,
  };
};
