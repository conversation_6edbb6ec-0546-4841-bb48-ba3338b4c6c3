import { useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { 
  setLoading, 
  setError, 
  setCryptocurrencies, 
  setChartData,
  setPrices 
} from '../store/slices/marketSlice';
import { 
  fetchMarketData, 
  fetchChartData, 
  fetchSimplePrices 
} from '../services/api';
import { SUPPORTED_CRYPTOCURRENCIES } from '../constants';

export const useMarketData = () => {
  const dispatch = useDispatch();
  const market = useSelector((state: RootState) => state.market);

  /**
   * Load market data for supported cryptocurrencies
   */
  const loadMarketData = useCallback(async (currency: string = 'usd') => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      const cryptocurrencies = await fetchMarketData(currency);
      dispatch(setCryptocurrencies(cryptocurrencies));
    } catch (error) {
      console.error('Error loading market data:', error);
      dispatch(setError(error instanceof Error ? error.message : 'Failed to load market data'));
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Load chart data for a specific cryptocurrency
   */
  const loadChartData = useCallback(async (
    cryptoId: string, 
    currency: string = 'usd', 
    days: string | number = '7'
  ) => {
    try {
      dispatch(setLoading(true));
      dispatch(setError(null));

      const chartData = await fetchChartData(cryptoId, currency, days);
      dispatch(setChartData({ cryptoId, data: chartData }));
    } catch (error) {
      console.error('Error loading chart data:', error);
      dispatch(setError(error instanceof Error ? error.message : 'Failed to load chart data'));
    } finally {
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Update prices for specific cryptocurrencies
   */
  const updatePrices = useCallback(async (
    coinIds?: string[], 
    currency: string = 'usd'
  ) => {
    try {
      const idsToFetch = coinIds || SUPPORTED_CRYPTOCURRENCIES.map(coin => coin.id);
      const prices = await fetchSimplePrices(idsToFetch, currency);
      
      // Transform to simple price object
      const simplePrices: { [key: string]: number } = {};
      Object.keys(prices).forEach(coinId => {
        simplePrices[coinId] = prices[coinId].usd;
      });
      
      dispatch(setPrices(simplePrices));
    } catch (error) {
      console.error('Error updating prices:', error);
      dispatch(setError(error instanceof Error ? error.message : 'Failed to update prices'));
    }
  }, [dispatch]);

  /**
   * Load market data on mount
   */
  useEffect(() => {
    loadMarketData();
  }, [loadMarketData]);

  return {
    // State
    cryptocurrencies: market.cryptocurrencies,
    chartData: market.chartData,
    prices: market.prices,
    isLoading: market.isLoading,
    error: market.error,
    lastUpdated: market.lastUpdated,
    
    // Actions
    loadMarketData,
    loadChartData,
    updatePrices,
  };
};
