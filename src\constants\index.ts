// API Configuration
export const API_CONFIG = {
  // CoinGecko for market data (free, no auth required)
  COINGECKO: {
    BASE_URL: 'https://api.coingecko.com/api/v3',
    ENDPOINTS: {
      COINS_MARKETS: '/coins/markets',
      COIN_DETAILS: '/coins',
      SIMPLE_PRICE: '/simple/price',
      COIN_HISTORY: '/coins/{id}/market_chart',
    },
  },
  // Luno API for account data (requires API keys)
  LUNO: {
    BASE_URL: 'https://api.luno.com/api/1',
    ENDPOINTS: {
      BALANCE: '/balance',
      TRANSACTIONS: '/transactions',
      ORDERS: '/orders',
      TRADES: '/trades',
      TICKERS: '/tickers',
      ORDERBOOK: '/orderbook',
      ACCOUNT_TRANSACTIONS: '/account_transactions',
    },
  },
  DEFAULT_CURRENCY: 'usd',
  DEFAULT_PER_PAGE: 50,
} as const;

// Luno API Configuration
export const LUNO_CONFIG = {
  // These will be set by user in app settings
  API_KEY_ID: '',
  API_KEY_SECRET: '',
  // Supported currency pairs on Luno
  CURRENCY_PAIRS: [
    'XBTZAR', // Bitcoin to South African Rand
    'ETHZAR', // Ethereum to South African Rand
    'XBTEUR', // Bitcoin to Euro
    'XBTGBP', // Bitcoin to British Pound
    'ETHEUR', // Ethereum to Euro
    'ETHGBP', // Ethereum to British Pound
    'XRPZAR', // XRP to South African Rand
    'BCHZAR', // Bitcoin Cash to South African Rand
    'LTCZAR', // Litecoin to South African Rand
    'USDCZAR', // USD Coin to South African Rand
  ],
  // Map Luno currency codes to our internal IDs
  CURRENCY_MAPPING: {
    'XBT': 'bitcoin',
    'ETH': 'ethereum',
    'XRP': 'ripple',
    'BCH': 'bitcoin-cash',
    'LTC': 'litecoin',
    'USDC': 'usd-coin',
  },
} as const;

// Supported Cryptocurrencies (Luno focus)
export const SUPPORTED_CRYPTOCURRENCIES = [
  {
    id: 'bitcoin',
    symbol: 'BTC',
    name: 'Bitcoin',
    color: '#F7931A',
  },
  {
    id: 'ethereum',
    symbol: 'ETH',
    name: 'Ethereum',
    color: '#627EEA',
  },
  {
    id: 'litecoin',
    symbol: 'LTC',
    name: 'Litecoin',
    color: '#BFBBBB',
  },
  {
    id: 'ripple',
    symbol: 'XRP',
    name: 'XRP',
    color: '#23292F',
  },
  {
    id: 'bitcoin-cash',
    symbol: 'BCH',
    name: 'Bitcoin Cash',
    color: '#8DC351',
  },
  {
    id: 'chainlink',
    symbol: 'LINK',
    name: 'Chainlink',
    color: '#375BD2',
  },
  {
    id: 'usd-coin',
    symbol: 'USDC',
    name: 'USD Coin',
    color: '#2775CA',
  },
  {
    id: 'tether',
    symbol: 'USDT',
    name: 'Tether',
    color: '#26A17B',
  },
] as const;

// App Theme Colors
export const COLORS = {
  primary: '#1E40AF',
  primaryDark: '#1E3A8A',
  secondary: '#10B981',
  secondaryDark: '#059669',
  accent: '#F59E0B',
  background: '#FFFFFF',
  backgroundDark: '#111827',
  surface: '#F9FAFB',
  surfaceDark: '#1F2937',
  text: '#111827',
  textDark: '#F9FAFB',
  textSecondary: '#6B7280',
  textSecondaryDark: '#9CA3AF',
  border: '#E5E7EB',
  borderDark: '#374151',
  success: '#10B981',
  error: '#EF4444',
  warning: '#F59E0B',
  info: '#3B82F6',
} as const;

// Chart Configuration
export const CHART_CONFIG = {
  TIME_RANGES: [
    { label: '1D', value: '1', days: 1 },
    { label: '7D', value: '7', days: 7 },
    { label: '1M', value: '30', days: 30 },
    { label: '3M', value: '90', days: 90 },
    { label: '1Y', value: '365', days: 365 },
    { label: 'ALL', value: 'max', days: null },
  ],
  DEFAULT_RANGE: '7',
  REFRESH_INTERVAL: 60000, // 1 minute
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  PORTFOLIO: '@portfolio_data',
  TRANSACTIONS: '@transactions_data',
  SETTINGS: '@app_settings',
  PRICE_CACHE: '@price_cache',
  LAST_UPDATE: '@last_update',
  LUNO_API_CREDENTIALS: '@luno_api_credentials',
  LUNO_ACCOUNT_DATA: '@luno_account_data',
} as const;

// App Settings Defaults
export const DEFAULT_SETTINGS = {
  currency: 'USD',
  theme: 'auto',
  notifications: {
    priceAlerts: true,
    portfolioUpdates: true,
    newsUpdates: false,
  },
  refreshInterval: 5, // minutes
} as const;

// Transaction Types
export const TRANSACTION_TYPES = [
  { value: 'buy', label: 'Buy', color: COLORS.success },
  { value: 'sell', label: 'Sell', color: COLORS.error },
  { value: 'transfer_in', label: 'Transfer In', color: COLORS.info },
  { value: 'transfer_out', label: 'Transfer Out', color: COLORS.warning },
] as const;

// Currency Symbols
export const CURRENCY_SYMBOLS = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  ZAR: 'R',
} as const;

// Number Formatting
export const FORMAT_CONFIG = {
  DECIMAL_PLACES: {
    PRICE: 2,
    AMOUNT: 8,
    PERCENTAGE: 2,
  },
  LARGE_NUMBER_THRESHOLD: 1000000,
} as const;

// Widget Configuration
export const WIDGET_CONFIG = {
  UPDATE_INTERVAL: 300000, // 5 minutes
  MAX_HOLDINGS_DISPLAY: 3,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  API_ERROR: 'Unable to fetch data. Please try again later.',
  INVALID_TRANSACTION: 'Invalid transaction data.',
  STORAGE_ERROR: 'Unable to save data locally.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  TRANSACTION_ADDED: 'Transaction added successfully',
  TRANSACTION_UPDATED: 'Transaction updated successfully',
  TRANSACTION_DELETED: 'Transaction deleted successfully',
  SETTINGS_SAVED: 'Settings saved successfully',
} as const;
