import { fetchSimplePrices, fetchMarketData } from './api';
import { 
  getPortfolioHoldings, 
  storePortfolioHoldings, 
  getTransactions,
  getPriceCache,
  storePriceCache 
} from './storage';
import { PortfolioHolding, Transaction, PortfolioSummary, Cryptocurrency } from '../types';
import { generateId, calculatePortfolioMetrics } from '../utils';

/**
 * Calculate portfolio holdings from transactions
 */
export const calculateHoldingsFromTransactions = async (): Promise<PortfolioHolding[]> => {
  try {
    const transactions = await getTransactions();
    const holdingsMap = new Map<string, {
      amount: number;
      totalInvested: number;
      transactions: Transaction[];
    }>();

    // Process transactions to calculate holdings
    transactions.forEach(transaction => {
      const cryptoId = transaction.cryptocurrency.id;
      const existing = holdingsMap.get(cryptoId) || {
        amount: 0,
        totalInvested: 0,
        transactions: [],
      };

      switch (transaction.type) {
        case 'buy':
        case 'transfer_in':
          existing.amount += transaction.amount;
          existing.totalInvested += transaction.totalValue;
          break;
        case 'sell':
        case 'transfer_out':
          existing.amount -= transaction.amount;
          // For sells, we reduce the total invested proportionally
          const sellRatio = transaction.amount / (existing.amount + transaction.amount);
          existing.totalInvested -= existing.totalInvested * sellRatio;
          break;
      }

      existing.transactions.push(transaction);
      holdingsMap.set(cryptoId, existing);
    });

    // Convert to PortfolioHolding array and filter out zero amounts
    const holdings: PortfolioHolding[] = [];
    
    for (const [cryptoId, holding] of holdingsMap) {
      if (holding.amount > 0.00000001) { // Filter out dust amounts
        const averageBuyPrice = holding.totalInvested / holding.amount;
        
        // We'll need to get current price from cache or API
        const currentPrice = await getCurrentPrice(cryptoId);
        const currentValue = holding.amount * currentPrice;
        const profitLoss = currentValue - holding.totalInvested;
        const profitLossPercentage = (profitLoss / holding.totalInvested) * 100;

        // Get cryptocurrency info from the first transaction
        const firstTransaction = holding.transactions[0];
        
        holdings.push({
          id: generateId(),
          cryptocurrency: {
            ...firstTransaction.cryptocurrency,
            current_price: currentPrice,
          } as Cryptocurrency,
          amount: holding.amount,
          averageBuyPrice,
          totalInvested: holding.totalInvested,
          currentValue,
          profitLoss,
          profitLossPercentage,
          lastUpdated: new Date().toISOString(),
        });
      }
    }

    return holdings;
  } catch (error) {
    console.error('Error calculating holdings from transactions:', error);
    throw error;
  }
};

/**
 * Get current price for a cryptocurrency
 */
const getCurrentPrice = async (cryptoId: string): Promise<number> => {
  try {
    // First try to get from cache
    const cache = await getPriceCache();
    if (cache && cache.prices[cryptoId]) {
      // Check if cache is not too old (5 minutes)
      if (Date.now() - cache.timestamp < 5 * 60 * 1000) {
        return cache.prices[cryptoId];
      }
    }

    // Fetch fresh price from API
    const prices = await fetchSimplePrices([cryptoId]);
    const price = prices[cryptoId]?.usd || 0;
    
    // Update cache
    const newCache = cache || { prices: {}, timestamp: Date.now() };
    newCache.prices[cryptoId] = price;
    newCache.timestamp = Date.now();
    await storePriceCache(newCache.prices);
    
    return price;
  } catch (error) {
    console.error('Error getting current price:', error);
    // Return cached price if available, otherwise 0
    const cache = await getPriceCache();
    return cache?.prices[cryptoId] || 0;
  }
};

/**
 * Update portfolio with latest prices
 */
export const updatePortfolioPrices = async (): Promise<PortfolioHolding[]> => {
  try {
    const holdings = await getPortfolioHoldings();
    
    if (holdings.length === 0) {
      return holdings;
    }

    // Get all unique crypto IDs
    const cryptoIds = [...new Set(holdings.map(h => h.cryptocurrency.id))];
    
    // Fetch latest prices
    const prices = await fetchSimplePrices(cryptoIds);
    
    // Update holdings with new prices
    const updatedHoldings = holdings.map(holding => {
      const cryptoId = holding.cryptocurrency.id;
      const priceData = prices[cryptoId];
      
      if (priceData) {
        const currentPrice = priceData.usd;
        const currentValue = holding.amount * currentPrice;
        const profitLoss = currentValue - holding.totalInvested;
        const profitLossPercentage = (profitLoss / holding.totalInvested) * 100;
        
        return {
          ...holding,
          cryptocurrency: {
            ...holding.cryptocurrency,
            current_price: currentPrice,
            price_change_percentage_24h: priceData.usd_24h_change,
          },
          currentValue,
          profitLoss,
          profitLossPercentage,
          lastUpdated: new Date().toISOString(),
        };
      }
      
      return holding;
    });

    // Store updated holdings
    await storePortfolioHoldings(updatedHoldings);
    
    // Update price cache
    const priceCache: { [key: string]: number } = {};
    Object.keys(prices).forEach(cryptoId => {
      priceCache[cryptoId] = prices[cryptoId].usd;
    });
    await storePriceCache(priceCache);
    
    return updatedHoldings;
  } catch (error) {
    console.error('Error updating portfolio prices:', error);
    throw error;
  }
};

/**
 * Calculate portfolio summary
 */
export const calculatePortfolioSummary = (holdings: PortfolioHolding[]): PortfolioSummary => {
  const metrics = calculatePortfolioMetrics(holdings);
  
  // Calculate 24h change
  const dayChange = holdings.reduce((sum, holding) => {
    const dayChangeValue = holding.currentValue * (holding.cryptocurrency.price_change_percentage_24h || 0) / 100;
    return sum + dayChangeValue;
  }, 0);
  
  const dayChangePercentage = metrics.totalValue > 0 ? (dayChange / metrics.totalValue) * 100 : 0;
  
  return {
    ...metrics,
    dayChange,
    dayChangePercentage,
    holdings,
    lastUpdated: new Date().toISOString(),
  };
};

/**
 * Refresh entire portfolio data
 */
export const refreshPortfolioData = async (): Promise<{
  holdings: PortfolioHolding[];
  summary: PortfolioSummary;
}> => {
  try {
    // First, recalculate holdings from transactions
    const calculatedHoldings = await calculateHoldingsFromTransactions();
    
    // Store the calculated holdings
    await storePortfolioHoldings(calculatedHoldings);
    
    // Update with latest prices
    const updatedHoldings = await updatePortfolioPrices();
    
    // Calculate summary
    const summary = calculatePortfolioSummary(updatedHoldings);
    
    return {
      holdings: updatedHoldings,
      summary,
    };
  } catch (error) {
    console.error('Error refreshing portfolio data:', error);
    throw error;
  }
};

/**
 * Get portfolio data (from storage with optional refresh)
 */
export const getPortfolioData = async (forceRefresh: boolean = false): Promise<{
  holdings: PortfolioHolding[];
  summary: PortfolioSummary;
}> => {
  try {
    if (forceRefresh) {
      return await refreshPortfolioData();
    }
    
    const holdings = await getPortfolioHoldings();
    const summary = calculatePortfolioSummary(holdings);
    
    return { holdings, summary };
  } catch (error) {
    console.error('Error getting portfolio data:', error);
    throw error;
  }
};
