// Core cryptocurrency types
export interface Cryptocurrency {
  id: string;
  symbol: string;
  name: string;
  image: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  last_updated: string;
}

// Portfolio holding types
export interface PortfolioHolding {
  id: string;
  cryptocurrency: Cryptocurrency;
  amount: number;
  averageBuyPrice: number;
  totalInvested: number;
  currentValue: number;
  profitLoss: number;
  profitLossPercentage: number;
  lastUpdated: string;
}

// Transaction types
export interface Transaction {
  id: string;
  type: 'buy' | 'sell' | 'transfer_in' | 'transfer_out';
  cryptocurrency: {
    id: string;
    symbol: string;
    name: string;
  };
  amount: number;
  price: number;
  totalValue: number;
  fee: number;
  date: string;
  notes?: string;
  exchange?: string;
}

// Portfolio summary types
export interface PortfolioSummary {
  totalValue: number;
  totalInvested: number;
  totalProfitLoss: number;
  totalProfitLossPercentage: number;
  dayChange: number;
  dayChangePercentage: number;
  holdings: PortfolioHolding[];
  lastUpdated: string;
}

// Chart data types
export interface ChartDataPoint {
  timestamp: number;
  price: number;
  volume?: number;
}

export interface ChartData {
  prices: ChartDataPoint[];
  market_caps?: ChartDataPoint[];
  total_volumes?: ChartDataPoint[];
}

// API response types
export interface CoinGeckoMarketResponse {
  data: Cryptocurrency[];
  status: {
    timestamp: string;
    error_code: number;
    error_message: string | null;
  };
}

export interface CoinGeckoPriceResponse {
  [key: string]: {
    usd: number;
    usd_24h_change: number;
  };
}

// Navigation types
export type RootStackParamList = {
  Home: undefined;
  Portfolio: undefined;
  Transactions: undefined;
  Analytics: undefined;
  Settings: undefined;
  AddTransaction: {
    cryptoId?: string;
  };
  EditTransaction: {
    transactionId: string;
  };
  CoinDetails: {
    coinId: string;
  };
};

// Widget types
export interface WidgetData {
  totalValue: string;
  dayChange: string;
  dayChangePercentage: string;
  topHoldings: Array<{
    symbol: string;
    value: string;
    change: string;
  }>;
}

// Settings types
export interface AppSettings {
  currency: 'USD' | 'EUR' | 'GBP' | 'ZAR';
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    priceAlerts: boolean;
    portfolioUpdates: boolean;
    newsUpdates: boolean;
  };
  refreshInterval: number; // in minutes
}

// Error types
export interface ApiError {
  message: string;
  code?: string;
  status?: number;
}

// Loading states
export interface LoadingState {
  isLoading: boolean;
  error: ApiError | null;
}

// Supported cryptocurrencies on Luno
export const LUNO_SUPPORTED_COINS = [
  'bitcoin',
  'ethereum',
  'litecoin',
  'ripple',
  'bitcoin-cash',
  'chainlink',
  'usd-coin',
  'tether',
] as const;

export type LunoSupportedCoin = typeof LUNO_SUPPORTED_COINS[number];

// Luno API types
export interface LunoApiCredentials {
  apiKeyId: string;
  apiKeySecret: string;
}

export interface LunoBalance {
  account_id: string;
  asset: string;
  balance: string;
  reserved: string;
  unconfirmed: string;
}

export interface LunoTransaction {
  row_index: number;
  timestamp: number;
  balance: string;
  available: string;
  available_delta: string;
  balance_delta: string;
  currency: string;
  description: string;
  details: {
    [key: string]: any;
  };
}

export interface LunoTicker {
  pair: string;
  timestamp: number;
  bid: string;
  ask: string;
  last_trade: string;
  rolling_24_hour_volume: string;
  status: string;
}

export interface LunoHolding {
  id: string;
  cryptocurrency: {
    id: string;
    symbol: string;
    name: string;
  };
  amount: number;
  availableAmount: number;
  reservedAmount: number;
  unconfirmedAmount: number;
  source: 'luno';
  lastUpdated: string;
}
