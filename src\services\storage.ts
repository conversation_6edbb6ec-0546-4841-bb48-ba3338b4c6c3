import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants';
import { PortfolioHolding, Transaction, AppSettings, Cryptocurrency } from '../types';

/**
 * Generic storage functions
 */
export const storeData = async (key: string, value: any): Promise<void> => {
  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(key, jsonValue);
  } catch (error) {
    console.error('Error storing data:', error);
    throw new Error('Failed to store data');
  }
};

export const getData = async <T>(key: string): Promise<T | null> => {
  try {
    const jsonValue = await AsyncStorage.getItem(key);
    return jsonValue != null ? JSON.parse(jsonValue) : null;
  } catch (error) {
    console.error('Error getting data:', error);
    return null;
  }
};

export const removeData = async (key: string): Promise<void> => {
  try {
    await AsyncStorage.removeItem(key);
  } catch (error) {
    console.error('Error removing data:', error);
    throw new Error('Failed to remove data');
  }
};

export const clearAllData = async (): Promise<void> => {
  try {
    await AsyncStorage.clear();
  } catch (error) {
    console.error('Error clearing all data:', error);
    throw new Error('Failed to clear all data');
  }
};

/**
 * Portfolio-specific storage functions
 */
export const storePortfolioHoldings = async (holdings: PortfolioHolding[]): Promise<void> => {
  await storeData(STORAGE_KEYS.PORTFOLIO, holdings);
};

export const getPortfolioHoldings = async (): Promise<PortfolioHolding[]> => {
  const holdings = await getData<PortfolioHolding[]>(STORAGE_KEYS.PORTFOLIO);
  return holdings || [];
};

export const clearPortfolioHoldings = async (): Promise<void> => {
  await removeData(STORAGE_KEYS.PORTFOLIO);
};

/**
 * Transaction-specific storage functions
 */
export const storeTransactions = async (transactions: Transaction[]): Promise<void> => {
  await storeData(STORAGE_KEYS.TRANSACTIONS, transactions);
};

export const getTransactions = async (): Promise<Transaction[]> => {
  const transactions = await getData<Transaction[]>(STORAGE_KEYS.TRANSACTIONS);
  return transactions || [];
};

export const addTransaction = async (transaction: Transaction): Promise<void> => {
  const transactions = await getTransactions();
  transactions.push(transaction);
  await storeTransactions(transactions);
};

export const updateTransaction = async (transactionId: string, updates: Partial<Transaction>): Promise<void> => {
  const transactions = await getTransactions();
  const index = transactions.findIndex(tx => tx.id === transactionId);
  
  if (index >= 0) {
    transactions[index] = { ...transactions[index], ...updates };
    await storeTransactions(transactions);
  } else {
    throw new Error('Transaction not found');
  }
};

export const deleteTransaction = async (transactionId: string): Promise<void> => {
  const transactions = await getTransactions();
  const filteredTransactions = transactions.filter(tx => tx.id !== transactionId);
  await storeTransactions(filteredTransactions);
};

export const clearTransactions = async (): Promise<void> => {
  await removeData(STORAGE_KEYS.TRANSACTIONS);
};

/**
 * Settings-specific storage functions
 */
export const storeSettings = async (settings: AppSettings): Promise<void> => {
  await storeData(STORAGE_KEYS.SETTINGS, settings);
};

export const getSettings = async (): Promise<AppSettings | null> => {
  return await getData<AppSettings>(STORAGE_KEYS.SETTINGS);
};

export const clearSettings = async (): Promise<void> => {
  await removeData(STORAGE_KEYS.SETTINGS);
};

/**
 * Price cache functions for offline support
 */
export const storePriceCache = async (prices: { [cryptoId: string]: number }): Promise<void> => {
  const cacheData = {
    prices,
    timestamp: Date.now(),
  };
  await storeData(STORAGE_KEYS.PRICE_CACHE, cacheData);
};

export const getPriceCache = async (): Promise<{ prices: { [cryptoId: string]: number }; timestamp: number } | null> => {
  return await getData(STORAGE_KEYS.PRICE_CACHE);
};

export const clearPriceCache = async (): Promise<void> => {
  await removeData(STORAGE_KEYS.PRICE_CACHE);
};

/**
 * Last update timestamp functions
 */
export const storeLastUpdate = async (timestamp: string): Promise<void> => {
  await storeData(STORAGE_KEYS.LAST_UPDATE, timestamp);
};

export const getLastUpdate = async (): Promise<string | null> => {
  return await getData<string>(STORAGE_KEYS.LAST_UPDATE);
};

/**
 * Backup and restore functions
 */
export const createBackup = async (): Promise<string> => {
  try {
    const [holdings, transactions, settings] = await Promise.all([
      getPortfolioHoldings(),
      getTransactions(),
      getSettings(),
    ]);
    
    const backup = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      data: {
        holdings,
        transactions,
        settings,
      },
    };
    
    return JSON.stringify(backup, null, 2);
  } catch (error) {
    console.error('Error creating backup:', error);
    throw new Error('Failed to create backup');
  }
};

export const restoreFromBackup = async (backupData: string): Promise<void> => {
  try {
    const backup = JSON.parse(backupData);
    
    if (!backup.version || !backup.data) {
      throw new Error('Invalid backup format');
    }
    
    const { holdings, transactions, settings } = backup.data;
    
    // Store the restored data
    if (holdings) await storePortfolioHoldings(holdings);
    if (transactions) await storeTransactions(transactions);
    if (settings) await storeSettings(settings);
    
  } catch (error) {
    console.error('Error restoring backup:', error);
    throw new Error('Failed to restore backup');
  }
};

/**
 * Storage info and cleanup functions
 */
export const getStorageInfo = async (): Promise<{ [key: string]: number }> => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const info: { [key: string]: number } = {};
    
    for (const key of keys) {
      const value = await AsyncStorage.getItem(key);
      info[key] = value ? value.length : 0;
    }
    
    return info;
  } catch (error) {
    console.error('Error getting storage info:', error);
    return {};
  }
};

export const cleanupOldData = async (maxAge: number = 30 * 24 * 60 * 60 * 1000): Promise<void> => {
  try {
    const priceCache = await getPriceCache();
    
    if (priceCache && Date.now() - priceCache.timestamp > maxAge) {
      await clearPriceCache();
    }
  } catch (error) {
    console.error('Error cleaning up old data:', error);
  }
};
