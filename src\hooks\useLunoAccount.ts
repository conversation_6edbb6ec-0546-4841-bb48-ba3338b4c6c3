import { useState, useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { lunoApiService } from '../services/lunoApi';
import { setHoldings } from '../store/slices/portfolioSlice';
import { addMultipleTransactions } from '../store/slices/transactionsSlice';
import { LunoBalance, LunoTransaction, LunoHolding } from '../types';

export const useLunoAccount = () => {
  const dispatch = useDispatch();
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [balances, setBalances] = useState<LunoBalance[]>([]);
  const [transactions, setTransactions] = useState<LunoTransaction[]>([]);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Check if connected to Luno
  const checkConnection = useCallback(async () => {
    try {
      const connected = lunoApiService.hasCredentials() && await lunoApiService.testConnection();
      setIsConnected(connected);
      return connected;
    } catch (err) {
      setIsConnected(false);
      return false;
    }
  }, []);

  // Load account data from Luno
  const loadAccountData = useCallback(async () => {
    if (!lunoApiService.hasCredentials()) {
      setError('Luno API credentials not set');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Check connection first
      const connected = await checkConnection();
      if (!connected) {
        setError('Not connected to Luno API');
        return;
      }

      // Fetch balances
      const balanceData = await lunoApiService.getBalance();
      setBalances(balanceData);

      // Fetch transactions
      const transactionData = await lunoApiService.getTransactions();
      setTransactions(transactionData);

      // Update last updated timestamp
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error loading Luno account data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load account data');
    } finally {
      setIsLoading(false);
    }
  }, [checkConnection]);

  // Sync Luno data with app portfolio
  const syncWithPortfolio = useCallback(async () => {
    if (!isConnected || balances.length === 0) {
      return;
    }

    try {
      setIsLoading(true);
      
      // Convert Luno balances to portfolio holdings
      const holdings = lunoApiService.convertBalanceToHoldings(balances);
      
      // Update portfolio in Redux
      dispatch(setHoldings(holdings));
      
      // Convert and add transactions if available
      if (transactions.length > 0) {
        const formattedTransactions = lunoApiService.convertTransactionsToPortfolioFormat(transactions);
        dispatch(addMultipleTransactions(formattedTransactions));
      }
      
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error syncing with portfolio:', err);
      setError(err instanceof Error ? err.message : 'Failed to sync with portfolio');
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, balances, transactions, dispatch]);

  // Connect to Luno API
  const connectToLuno = useCallback(async (apiKeyId: string, apiKeySecret: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      await lunoApiService.setCredentials(apiKeyId, apiKeySecret);
      setIsConnected(true);
      
      // Load account data after connecting
      await loadAccountData();
      
      return true;
    } catch (err) {
      console.error('Error connecting to Luno:', err);
      setError(err instanceof Error ? err.message : 'Failed to connect to Luno');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [loadAccountData]);

  // Disconnect from Luno API
  const disconnectFromLuno = useCallback(async () => {
    try {
      await lunoApiService.clearCredentials();
      setIsConnected(false);
      setBalances([]);
      setTransactions([]);
      return true;
    } catch (err) {
      console.error('Error disconnecting from Luno:', err);
      setError(err instanceof Error ? err.message : 'Failed to disconnect from Luno');
      return false;
    }
  }, []);

  // Format balances for display
  const getFormattedHoldings = useCallback((): LunoHolding[] => {
    return lunoApiService.convertBalanceToHoldings(balances);
  }, [balances]);

  // Check connection on mount
  useEffect(() => {
    checkConnection();
  }, [checkConnection]);

  return {
    // State
    isConnected,
    isLoading,
    error,
    balances,
    transactions,
    lastUpdated,
    
    // Formatted data
    holdings: getFormattedHoldings(),
    
    // Actions
    checkConnection,
    loadAccountData,
    syncWithPortfolio,
    connectToLuno,
    disconnectFromLuno,
  };
};
