import axios, { AxiosResponse } from 'axios';
import { API_CONFIG, SUPPORTED_CRYPTOCURRENCIES } from '../constants';
import { Cryptocurrency, ChartData, ApiError } from '../types';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_CONFIG.COINGECKO.BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Response Error:', error);
    
    const apiError: ApiError = {
      message: error.response?.data?.message || error.message || 'Network error',
      code: error.response?.data?.code || error.code,
      status: error.response?.status,
    };
    
    return Promise.reject(apiError);
  }
);

/**
 * Fetch market data for supported cryptocurrencies
 */
export const fetchMarketData = async (
  currency: string = API_CONFIG.DEFAULT_CURRENCY,
  perPage: number = API_CONFIG.DEFAULT_PER_PAGE
): Promise<Cryptocurrency[]> => {
  try {
    const coinIds = SUPPORTED_CRYPTOCURRENCIES.map(coin => coin.id).join(',');
    
    const response: AxiosResponse<Cryptocurrency[]> = await api.get(
      API_CONFIG.COINGECKO.ENDPOINTS.COINS_MARKETS,
      {
        params: {
          vs_currency: currency,
          ids: coinIds,
          order: 'market_cap_desc',
          per_page: perPage,
          page: 1,
          sparkline: false,
          price_change_percentage: '24h',
        },
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error fetching market data:', error);
    throw error;
  }
};

/**
 * Fetch simple price data for specific cryptocurrencies
 */
export const fetchSimplePrices = async (
  coinIds: string[],
  currency: string = API_CONFIG.DEFAULT_CURRENCY
): Promise<{ [key: string]: { usd: number; usd_24h_change: number } }> => {
  try {
    const response = await api.get(API_CONFIG.COINGECKO.ENDPOINTS.SIMPLE_PRICE, {
      params: {
        ids: coinIds.join(','),
        vs_currencies: currency,
        include_24hr_change: true,
      },
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching simple prices:', error);
    throw error;
  }
};

/**
 * Fetch detailed coin information
 */
export const fetchCoinDetails = async (
  coinId: string,
  localization: boolean = false,
  tickers: boolean = false,
  marketData: boolean = true,
  communityData: boolean = false,
  developerData: boolean = false,
  sparkline: boolean = false
): Promise<any> => {
  try {
    const response = await api.get(`${API_CONFIG.COINGECKO.ENDPOINTS.COIN_DETAILS}/${coinId}`, {
      params: {
        localization,
        tickers,
        market_data: marketData,
        community_data: communityData,
        developer_data: developerData,
        sparkline,
      },
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching coin details:', error);
    throw error;
  }
};

/**
 * Fetch historical chart data for a cryptocurrency
 */
export const fetchChartData = async (
  coinId: string,
  currency: string = API_CONFIG.DEFAULT_CURRENCY,
  days: string | number = '7'
): Promise<ChartData> => {
  try {
    const endpoint = API_CONFIG.COINGECKO.ENDPOINTS.COIN_HISTORY.replace('{id}', coinId);
    
    const response = await api.get(endpoint, {
      params: {
        vs_currency: currency,
        days: days,
        interval: days === '1' ? 'hourly' : 'daily',
      },
    });
    
    const { prices, market_caps, total_volumes } = response.data;
    
    // Transform the data to our format
    const chartData: ChartData = {
      prices: prices.map(([timestamp, price]: [number, number]) => ({
        timestamp,
        price,
      })),
      market_caps: market_caps?.map(([timestamp, marketCap]: [number, number]) => ({
        timestamp,
        price: marketCap,
      })),
      total_volumes: total_volumes?.map(([timestamp, volume]: [number, number]) => ({
        timestamp,
        price: volume,
      })),
    };
    
    return chartData;
  } catch (error) {
    console.error('Error fetching chart data:', error);
    throw error;
  }
};

/**
 * Fetch trending cryptocurrencies
 */
export const fetchTrendingCoins = async (): Promise<any> => {
  try {
    const response = await api.get('/search/trending');
    return response.data;
  } catch (error) {
    console.error('Error fetching trending coins:', error);
    throw error;
  }
};

/**
 * Search for cryptocurrencies
 */
export const searchCoins = async (query: string): Promise<any> => {
  try {
    const response = await api.get('/search', {
      params: { query },
    });
    return response.data;
  } catch (error) {
    console.error('Error searching coins:', error);
    throw error;
  }
};

/**
 * Fetch global market data
 */
export const fetchGlobalData = async (): Promise<any> => {
  try {
    const response = await api.get('/global');
    return response.data;
  } catch (error) {
    console.error('Error fetching global data:', error);
    throw error;
  }
};

// Export the configured axios instance for custom requests
export { api };

// Export API configuration for use in other parts of the app
export { API_CONFIG };
